use crate::methods::utils::RateLimiting;
use crate::structs::enums::RiotPlatforms;
use crate::structs::helper::{
    get_buddies, get_flex, get_player_cards, get_player_titles, get_skins, get_sprays,
};
use crate::structs::http_clients::{redis_fetch_ipv6, RedisFetchIPv6Options};
use crate::structs::paths::StoreFeaturedPath;
use crate::structs::pvp_api::EntitlementJWT;
use crate::structs::responses::{
    StoreFeaturedV1, StoreFeaturedV1Response, StoreFeaturedV2, StoreFeaturedV2Item,
    StoreFeaturedV2Response,
};
use crate::{build_riot_headers, error_handler, AppState, ErrorCodes, VALORANT_TYPE_IDS};
use crate::structs::errors::SendError;
use axum::body::Body;
use axum::extract::{Path, State};
use axum::response::Response;
use axum::Extension;
use jsonwebtoken::{decode, Algorithm, DecodingKey, Validation};
use mongodb::bson::DateTime;
use serde_json::json;
use std::collections::HashSet;
use std::sync::Arc;
use valorant_api::response_types::store_front_v2::{BundleItem, StoreFrontV2};

#[utoipa::path(
	get,
	path = "/valorant/{version}/store-featured",
	tag = "valorant",
	params(
		("version" = String, Path, description = "API version (v1, v2)")
	),
	responses(
		(status = 200, description = "Store featured items retrieved successfully", body = serde_json::Value),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Store data not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
#[allow(non_snake_case)]
pub async fn StoreFeatured(
    Path(path): Path<StoreFeaturedPath>,
    State(app_state): State<Arc<AppState>>,
    Extension(extension): Extension<Arc<RateLimiting>>,
) -> Response {
    let rl = extension.as_ref();
    let headers = build_riot_headers(&RiotPlatforms::PC).await;
    let entitlement = headers
        .get("X-Riot-Entitlements-JWT")
        .unwrap()
        .to_str()
        .unwrap();
    let mut validation = Validation::new(Algorithm::RS256);
    validation.insecure_disable_signature_validation();
    validation.validate_exp = false;
    validation.required_spec_claims = HashSet::new();
    let jwt_decode =
        decode::<EntitlementJWT>(&entitlement, &DecodingKey::from_secret(&[]), &validation)
            .unwrap();
    //let conn = redis.get().await.expect("Failed to get Redis connection from pool");
    let conn = app_state.redis.clone();
    let store = redis_fetch_ipv6::<StoreFrontV2>(RedisFetchIPv6Options {
        method: "POST".to_string(),
        url: format!(
            "https://pd.eu.a.pvp.net/store/v3/storefront/{}",
            jwt_decode.claims.sub
        ),
        headers,
        data: json!({}),
        redis_client: Some(conn),
        store: "store_featured".to_string(),
        ..RedisFetchIPv6Options::default()
    })
    .await;
    if store.is_err() {
        return error_handler(vec![ErrorCodes::FetchingResource]);
    }
    let store_json = store.unwrap();
    rl.redis_cache_ttl
        .store(store_json.ttl as isize, std::sync::atomic::Ordering::SeqCst);
    if !store_json.is_from_redis {
        rl.background_requests
            .fetch_add(1, std::sync::atomic::Ordering::SeqCst);
    }
    match path.version.as_str() {
        "v1" => {
            let v1 = StoreFeaturedV1 {
                FeaturedBundle: store_json.data.featured_bundle,
            };
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(
                    serde_json::to_string(&StoreFeaturedV1Response {
                        status: 200,
                        data: v1,
                    })
                    .unwrap(),
                ))
                .unwrap()
        }
        "v2" => {
            let mut parsed_bundles = vec![];
            for x in store_json.data.featured_bundle.bundles.into_iter() {
                let items = v2_item_parser(x.items).await;
                let expires_at = DateTime::from_millis(
                    ((x.duration_remaining_in_seconds * 1000) as i64)
                        + chrono::Utc::now().timestamp_millis(),
                );
                parsed_bundles.push(StoreFeaturedV2 {
                    bundle_uuid: x.data_asset_id.to_string(),
                    bundle_price: items
                        .iter()
                        .fold(0, |acc: u32, e| acc + e.discounted_price as u32),
                    whole_sale_only: x.wholesale_only,
                    items,
                    seconds_remaining: x.duration_remaining_in_seconds,
                    expires_at: expires_at.try_to_rfc3339_string().unwrap(),
                });
            }
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(
                    serde_json::to_string(&StoreFeaturedV2Response {
                        status: 200,
                        data: parsed_bundles,
                    })
                    .unwrap(),
                ))
                .unwrap()
        }
        _ => error_handler(vec![ErrorCodes::UnknownVersion]),
    }
}

async fn v2_item_parser(items: Vec<BundleItem>) -> Vec<StoreFeaturedV2Item> {
    let mut formatted: Vec<StoreFeaturedV2Item> = vec![];
    let item_type = &VALORANT_TYPE_IDS;
    for x in items.iter() {
        let item_type = item_type
            .get(x.item.item_type_id.to_string().as_str())
            .unwrap();
        let parent: PartialParentData = match *item_type {
            "skin_level" => {
                let skins = get_skins().await;
                let parent = skins
                    .iter()
                    .find(|&i| {
                        i.levels
                            .iter()
                            .find(|&k| k.uuid == x.item.item_id.to_string())
                            .is_some()
                    })
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.to_string(),
                    name: parent.displayName.to_string(),
                    image: parent.displayIcon.clone(),
                }
            }
            "buddy" => {
                let buddies = get_buddies().await;
                let parent = buddies
                    .iter()
                    .find(|&i| {
                        i.levels
                            .iter()
                            .find(|&k| k.uuid == x.item.item_id.to_string())
                            .is_some()
                    })
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.to_string(),
                    name: parent.displayName.to_string(),
                    image: Some(parent.displayIcon.clone()),
                }
            }
            "player_card" => {
                let player_cards = get_player_cards().await;
                let parent = player_cards
                    .iter()
                    .find(|&i| i.uuid == x.item.item_id.to_string())
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.to_string(),
                    name: parent.displayName.to_string(),
                    image: Some(parent.displayIcon.clone()),
                }
            }
            "spray" => {
                let sprays = get_sprays().await;
                let parent = sprays
                    .iter()
                    .find(|&i| i.uuid == x.item.item_id.to_string())
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.to_string(),
                    name: parent.displayName.to_string(),
                    image: Some(parent.displayIcon.clone()),
                }
            }
            "player_title" => {
                let player_titles = get_player_titles().await;
                let parent = player_titles
                    .iter()
                    .find(|&i| i.uuid == x.item.item_id.to_string())
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.clone(),
                    name: parent.displayName.clone().unwrap_or("".to_string()),
                    image: None,
                }
            }
            "flex" => {
                let flex = get_flex().await;
                let parent = flex
                    .iter()
                    .find(|&i| i.uuid == x.item.item_id.to_string())
                    .unwrap();
                PartialParentData {
                    uuid: parent.uuid.clone(),
                    name: parent.displayName.clone().unwrap_or("".to_string()),
                    image: parent.displayIcon.clone(),
                }
            }
            _ => PartialParentData {
                uuid: "".to_string(),
                name: "".to_string(),
                image: None,
            },
        };
        formatted.push(StoreFeaturedV2Item {
            uuid: parent.uuid,
            name: parent.name,
            image: parent.image,
            r#type: item_type.to_string(),
            amount: x.item.amount,
            discount_percent: x.discount_percent,
            base_price: x.base_price,
            discounted_price: x.discounted_price as i32,
            promo_item: x.is_promo_item,
        })
    }
    formatted
}

pub struct PartialParentData {
    pub uuid: String,
    pub name: String,
    pub image: Option<String>,
}

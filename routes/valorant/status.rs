use axum::body::Body;
use axum::extract::Path;
use axum::response::Response;
use crate::{check_affinity, error_handler, fetch, structs::{paths::StatusPath, pvp_api::PVPStatusResponse}, ErrorCodes};
use crate::structs::http_clients::FetchOptions;
use crate::structs::responses::{StatusV1, StatusV1Data};
use crate::structs::errors::SendError;

#[utoipa::path(
	get,
	path = "/valorant/v1/status/{affinity}",
	tag = "valorant",
	params(
		("affinity" = String, Path, description = "Region/affinity (e.g., na, eu, ap, kr)")
	),
	responses(
		(status = 200, description = "Status retrieved successfully", body = StatusV1),
		(status = 400, description = "Bad Request", body = SendError),
		(status = 404, description = "Region not found", body = SendError),
		(status = 500, description = "Internal Server Error", body = SendError)
	)
)]
#[allow(non_snake_case)]
pub async fn Status(Path(path): Path<StatusPath>) -> Response {
    let validate_region = check_affinity(&path.affinity);
    if !validate_region {
        return error_handler(vec![ErrorCodes::InvalidRegion]);
    }
    let status = fetch::<PVPStatusResponse>(
        FetchOptions {
            url: format!(
                "https://valorant.secure.dyn.riotcdn.net/channels/public/x/status/{}.json",
                &path.affinity.to_lowercase()
            ),
            ..FetchOptions::default()
        }
    )
        .await;
    match status {
        Ok(v) => {
            if v.status == 200 {
                Response::builder()
                    .status(200)
                    .header("Content-Type", "application/json")
                    .body(Body::from(serde_json::to_string(&StatusV1 {
                        status: v.status,
                        data: StatusV1Data {
                            maintenances: v.data.maintenances,
                            incidents: v.data.incidents,
                        },
                    }).unwrap())).unwrap()
            } else {
                error_handler(vec![ErrorCodes::FetchingResource])
            }
        }
        Err(_e) => {
            error_handler(vec![ErrorCodes::InternalError])
        }
    }
}

use crate::structs::database::c_deserialize;
use mongodb::bson::DateTime;
use serde::{ Deserialize, Serialize };
use std::collections::HashMap;
use utoipa::ToSchema;

#[derive(Serialize, Deserialize, Debug)]
pub struct EntitlementJWT {
	pub entitlements: Vec<String>,
	pub at_hash: String,
	pub sub: String,
	pub iss: String,
	pub iat: u64,
	pub jti: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierMatches {
	pub id: String,
	pub leagueMatchHistory: Vec<PVPPremierMatchesLeague>,
	pub tournamentMatchHistory: Vec<PVPPremierMatchesTournament>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierMatchesLeague {
	pub matchId: String,
	pub leaguePointsBefore: i32,
	pub leaguePointsAfter: i32,
	pub leaguePointsEarned: i32,
	pub startTime: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierMatchesTournament {
	pub tournamentId: String,
	pub finalPlacement: i32,
	pub finalPlacementLeaguePointsBonus: i32,
	pub leaguePointsBefore: i32,
	pub leaguePointsAfter: i32,
	pub leaguePointsEarned: i32,
	pub matchEntries: HashMap<String, i32>,
	pub tournamentMatchData: HashMap<String, PVPPremierMatchesTournamentMatchData>,
	pub startTime: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierMatchesTournamentMatchData {
	pub points: i32,
	pub roundNumber: i32,
	pub totalRounds: i32,
	pub bracketType: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColors {
	pub Type: String,
	pub Name: String,
	pub Class: String,
	pub Properties: FModelPremierColorsProperties,
	pub Rows: HashMap<String, FModelPremierColorsRow>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColorsRow {
	pub ColorName: FModelPremierColorsRowColorName,
	pub Guid: String,
	pub Color: FModelPremierColorsRowColor,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColorsRowColorName {
	pub CultureInvariantString: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColorsRowColor {
	pub R: f32,
	pub G: f32,
	pub B: f32,
	pub A: f32,
	pub Hex: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColorsProperties {
	pub RowStruct: FModelPremierColorsPropertiesRowStruct,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct FModelPremierColorsPropertiesRowStruct {
	pub ObjectName: String,
	pub ObjectPath: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierLeaderboard {
	#[serde(rename = "type")]
	pub type_: String,
	pub grouping: String,
	pub season: String,
	pub region: String,
	pub name: String,
	pub startRank: Option<i32>,
	pub endRank: Option<i32>,
	pub size: i32,
	pub rankings: Vec<PremierLeaderboardRanking>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PremierLeaderboardRanking {
	pub entityId: String,
	pub ranking: i32,
	pub score: i32,
	pub anonymous: bool,
	pub lastPlayedDate: String,
	pub additionalInfo: PremierLeaderboardRankingAdditionalInfo,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PremierLeaderboardRankingAdditionalInfo {
	pub rosterName: String,
	pub tournamentPlacement: Option<i32>,
	pub wins: i32,
	pub losses: i32,
	pub roundWins: i32,
	pub roundLosses: i32,
	pub tag: String,
	pub customization: PremierLeaderboardRankingCustomizations,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PremierLeaderboardRankingCustomizations {
	pub icon: String,
	pub primaryColor: String,
	pub secondaryColor: String,
	pub tertiaryColor: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierSeasons {
	pub PremierSeasons: Option<Vec<PremierSeason>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct EventScheduleTime {
	#[serde(deserialize_with = "c_deserialize")]
	pub StartDateTime: DateTime,
	#[serde(deserialize_with = "c_deserialize")]
	pub EndDateTime: DateTime,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct ConferenceSchedule {
	pub Conference: String,
	pub Timezone: String,
	pub ScheduleTimes: Vec<EventScheduleTime>,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub StartDateTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EndDateTime: DateTime,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Event {
	pub ID: String,
	pub Type: String,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub StartDateTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EndDateTime: DateTime,
	pub SchedulePerConference: Option<HashMap<String, ConferenceSchedule>>,
	pub MapSelectionStrategy: String,
	pub MapPoolMapIDs: Vec<String>,
	pub PointsRequiredToParticipate: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct ScheduledEvent {
	pub EventID: String,
	pub Conference: String,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub StartDateTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EndDateTime: DateTime,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct Division {
	pub Division: i32,
	pub DivisionName: String,
	pub DivisionGroup: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct DivisionThreshold {
	pub playoffThresholdType: String,
	pub playoffThresholdValue: i32,
	pub promotionThresholdType: String,
	pub promotionThresholdValue: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct EntitlementReward {
	pub ItemTypeID: String,
	pub ItemID: String,
	pub Amount: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct DivisionWinnerRewards {
	pub EntitlementRewards: Vec<EntitlementReward>,
	pub WalletRewards: Vec<EntitlementReward>,
	pub CounterRewards: Option<Vec<EntitlementReward>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct ParticipationRewards {
	pub EntitlementRewards: Vec<EntitlementReward>,
	pub WalletRewards: Vec<EntitlementReward>,
	pub CounterRewards: Option<Vec<EntitlementReward>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct TournamentWinnerRewards {
	pub EntitlementRewards: Vec<EntitlementReward>,
	pub WalletRewards: Vec<EntitlementReward>,
	pub CounterRewards: Option<Vec<EntitlementReward>>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PremierSeason {
	pub ID: String,
	pub CompetitiveSeasonID: String,
	pub IsActive: bool,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub StartTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EndTime: DateTime,
	pub Events: Vec<Event>,
	pub ScheduledEvents: Vec<ScheduledEvent>,
	pub Conferences: Vec<PremierConference>,
	pub Divisions: Vec<Division>,
	pub DivisionThresholds: Vec<DivisionThreshold>,
	pub ChampionshipPointRequirement: i32,
	pub ChampionshipEventID: String,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EnrollmentPhaseStartDateTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub EnrollmentPhaseEndDateTime: DateTime,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub LeaderboardFinalizationDateTime: DateTime,
	pub ParticipationRewardsActIDs: Vec<String>,
	pub ParticipationRewards: ParticipationRewards,
	pub TournamentWinnerRewards: TournamentWinnerRewards,
	pub DivisionWinnerRewards: HashMap<String, DivisionWinnerRewards>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPPremierConferences {
	pub PremierConferences: Vec<PremierConference>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct PremierConference {
	pub id: String,
	pub key: String,
	pub isSuper: Option<bool>,
	pub gamePods: Vec<String>,
	pub timezone: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPNameServiceV2 {
	pub DisplayName: String,
	pub Subject: String,
	pub GameName: String,
	pub TagLine: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPCustomGameConfig {
	pub Enabled: bool,
	pub EnabledMaps: Vec<String>,
	pub EnabledModes: Vec<String>,
	pub Queues: Vec<PVPCustomGameConfigQueue>,
	pub GamePodPingServiceInfo: HashMap<String, PodPingInfo>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPCustomGameConfigQueue {
	pub QueueID: String,
	pub Enabled: bool,
	pub TeamSize: i32,
	pub NumTeams: i32,
	pub MaxPartySize: i32,
	pub MinPartySize: i32,
	pub InvalidPartySizes: Vec<i32>,
	pub MaxPartySizeHighSkill: i32,
	pub HighSkillTier: i32,
	pub MaxSkillTier: i32,
	pub AllowFullPartyBypassSkillRestrictions: bool,
	pub Mode: String,
	pub IsRanked: bool,
	pub IsTournament: bool,
	pub RequireRoster: bool,
	pub Priority: i32,
	pub PartyMaxCompetitiveTierRange: i32,
	pub PartyMaxCompetitiveTierRangePlacementBuffer: i32,
	pub FullPartyMaxCompetitiveTierRange: i32,
	pub PartySkillDisparityCompetitiveTiersCeilings: HashMap<String, i32>,
	pub UseAccountLevelRequirement: bool,
	pub MinimumAccountLevelRequired: i32,
	pub GameRules: std::collections::HashMap<String, String>,
	pub SupportedPlatformTypes: Vec<String>,
	// or use Vec<String> if needed
	pub DisabledContent: Vec<serde_json::Value>,
	// or use Vec<serde_json::Value> if content type is unknown
	pub queueFieldA: Vec<serde_json::Value>,
	// or use Vec<serde_json::Value> if content type is unknown
	pub NextScheduleChangeSeconds: i32,
	pub TimeUntilNextScheduleChangeSeconds: i32,
	pub MapWeights: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PodPingInfo {
	pub SecurityHash: i64,
	pub ObfuscatedIP: i64,
	pub PingProxyAddress: String,
	pub PingProxyAddresses: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct PVPStatusResponse {
	pub id: String,
	pub name: String,
	pub locales: Vec<String>,
	pub maintenances: Vec<StatusIncident>,
	pub incidents: Vec<StatusIncident>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StatusIncident {
	pub id: u32,
	pub maintenance_status: Option<String>,
	pub incident_severity: String,
	pub titles: Vec<StatusIncidentContent>,
	pub updates: Vec<StatusIncidentUpdate>,
	pub created_at: String,
	pub updated_at: String,
	pub archive_at: Option<String>,
	pub platforms: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StatusIncidentContent {
	pub locale: String,
	pub content: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[allow(non_snake_case)]
pub struct StatusIncidentUpdate {
	pub id: u32,
	pub author: String,
	pub publish: bool,
	pub publish_locations: Vec<String>,
	pub translations: Vec<StatusIncidentContent>,
	pub created_at: String,
	pub updated_at: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct APIAccountRiotGames {
	pub puuid: String,
	pub alias: APIAccountRiotGamesAlias,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct APIAccountRiotGamesAlias {
	pub game_name: String,
	pub tag_line: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMatchHistoryV1 {
	pub Subject: String,
	pub BeginIndex: i32,
	pub EndIndex: i32,
	pub Total: i32,
	pub History: Vec<PVPMatchHistoryV1Match>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMatchHistoryV1Match {
	pub MatchID: String,
	pub GameStartTime: i64,
	pub QueueID: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMMRCompetitiveUpdatesV1 {
	pub Version: i32,
	pub Subject: String,
	pub Matches: Vec<PVPMMRCompetitiveUpdatesV1Match>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMMRCompetitiveUpdatesV1Match {
	pub MatchID: String,
	pub MapID: String,
	pub SeasonID: String,
	pub MatchStartTime: i64,
	pub TierAfterUpdate: i32,
	pub TierBeforeUpdate: i32,
	pub RankedRatingAfterUpdate: i32,
	pub RankedRatingBeforeUpdate: i32,
	pub RankedRatingEarned: i32,
	pub RankedRatingPerformanceBonus: i32,
	pub CompetitiveMovement: String,
	pub AFKPenalty: i32,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMMRPlayerV1 {
	pub Version: i64,
	pub Subject: String,
	pub QueueSkills: HashMap<String, PVPMMRPlayerV1QueueSkills>,
	pub DerankProtectedGamesRemaining: i32,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMMRPlayerV1QueueSkills {
	pub TotalGamesNeededForRating: i32,
	pub TotalGamesNeededForLeaderboard: i32,
	pub CurrentSeasonGamesNeededForRating: i32,
	pub SeasonalInfoBySeasonID: Option<HashMap<String, PVPMMRPlayerV1QueueSkillsSeasonalInfo>>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[allow(non_snake_case)]
pub struct PVPMMRPlayerV1QueueSkillsSeasonalInfo {
	pub SeasonID: String,
	pub NumberOfWins: i32,
	pub NumberOfWinsWithPlacements: i32,
	pub NumberOfGames: i32,
	pub Rank: i32,
	pub CapstoneWins: i32,
	pub LeaderboardRank: i32,
	pub CompetitiveTier: i32,
	pub RankedRating: i32,
	pub WinsByTier: Option<HashMap<String, i32>>,
	pub GamesNeededForRating: i32,
	pub TotalWinsNeededForRank: i32,
}

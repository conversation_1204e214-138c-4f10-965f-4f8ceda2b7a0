use crate::{
    VALORANT_CONTENT_LOCALS, VALORANT_PLATFORMS, VALORANT_REGIONS, VALORANT_WEBSITE_CATEGORIES,
    VALORANT_WEBSITE_COUNTRIES,
};
use axum::body::Body;
use axum::http::header::CONTENT_TYPE;
use axum::http::StatusCode;
use axum::response::Response;
use serde::Serialize;
use serde_json::Value;
use utoipa::ToSchema;

#[derive(Debug)]
pub enum ErrorCodes {
    Base404,
    Base400,
    Base401,
    Base403,
    Base429,
    InvalidFile,
    InternalError,
    UnknownVersion,
    ParsingError,
    InvalidRegion,
    FileNotFound,
    InvalidCountryCode,
    InvalidWebsiteCategory,
    FetchingResource,
    UnknownRawType,
    UserParsingError,
    RawV1UserParsingError,
    AssistV1NamesParsingError,
    RedisError,
    PremierEndpointTemporary<PERSON>ssues,
    PremierTeamNotFound,
    PremierSearchInvalidDivision,
    PremierSearchInvalidDivisionSize,
    PremierSearchInvalidDivisionSizeSuper,
    PremierSearchInvalidConference,
    PremierSearchMultipleQueries,
    DatabaseError,
    AccountNotFound,
    RegionNotFound,
    AccountMatchFetchError,
    MMRNoDataAvailable,
    MatchNotFound,
    InvalidMode,
    InvalidMap,
    MissingSizeQuery,
    InvalidSizeOrPageQuery,
    InvalidSizeOfSize,
    InvalidSizeOfPage,
    InvalidSeason,
    InvalidNameQuery,
    InvalidTagQuery,
    NotFoundInLeaderboard,
    InvalidLeague,
    InvalidLocaleContent,
    InvalidCrosshairCode,
    DuplicateSeasonQuery,
    LeaderboardMetadataNotFound,
    InvalidPlatform,
    InvalidUUID,
    InvalidStartIndex,
    RiotImplementationRemoved,
}

#[derive(Serialize, ToSchema)]
pub struct APIError {
    code: u16,
    message: String,
    pub status: u16,
    details: Option<Value>,
}

#[derive(Serialize, ToSchema)]
pub struct SendError {
    pub errors: Vec<APIError>,
}

#[allow(unreachable_patterns)]
pub fn get_error(error: &ErrorCodes) -> APIError {
    match error {
        ErrorCodes::Base404 => APIError {
            status: 404,
            code: 0,
            message: String::from("Route not found"),
            details: None,
        },
        ErrorCodes::Base400 => APIError {
            code: 0,
            message: String::from(
                "Bad Request",
            ),
            status: 400,
            details: None,
        },
        ErrorCodes::Base401 => APIError {
            code: 0,
            message: String::from(
                "Unauthorized",
            ),
            status: 401,
            details: None,
        },
        ErrorCodes::Base403 => APIError {
            code: 0,
            message: String::from(
                "Forbidden",
            ),
            status: 403,
            details: None,
        },
        ErrorCodes::Base429 => APIError {
            code: 0,
            message: String::from(
                "Rate limit exceeded, please try again later. For further information check the headers of the response.",
            ),
            status: 429,
            details: None,
        },
        ErrorCodes::InternalError => APIError {
            code: 1,
            message: String::from("Internal Error"),
            status: 500,
            details: None,
        },
        ErrorCodes::UnknownVersion => APIError {
            code: 2,
            message: String::from("API Version not implemented"),
            status: 501,
            details: None,
        },
        ErrorCodes::FileNotFound => APIError {
            status: 404,
            code: 3,
            message: String::from("File not found"),
            details: None,
        },
        ErrorCodes::InvalidFile => APIError {
            code: 4,
            message: String::from("Invalid File, make sure your File is not corrupted"),
            status: 400,
            details: None,
        },
        ErrorCodes::ParsingError => APIError {
            code: 5,
            message: String::from("Error while parsing JSON"),
            status: 500,
            details: None,
        },
        ErrorCodes::InvalidRegion => APIError {
            code: 6,
            message: String::from("Invalid region"),
            status: 400,
            details: Some(serde_json::to_value(VALORANT_REGIONS).unwrap()),
        },
        ErrorCodes::InvalidRegion => APIError {
            code: 7,
            message: String::from("Invalid country code"),
            status: 400,
            details: Some(serde_json::to_value(VALORANT_WEBSITE_COUNTRIES).unwrap()),
        },
        ErrorCodes::InvalidWebsiteCategory => APIError {
            code: 8,
            message: String::from("Invalid website category"),
            status: 400,
            details: Some(serde_json::to_value(VALORANT_WEBSITE_CATEGORIES).unwrap()),
        },
        ErrorCodes::FetchingResource => APIError {
            code: 9,
            message: String::from("Not able to fetch needed resource to build response"),
            status: 500,
            details: None,
        },
        ErrorCodes::UnknownRawType => APIError {
            code: 10,
            message: String::from("Unknown type"),
            status: 400,
            details: Some(
                serde_json::to_value(["matchdetails", "matchhistory", "mmr", "competitiveupdates"])
                    .unwrap(),
            ),
        },
        ErrorCodes::UserParsingError => APIError {
            code: 11,
            message: String::from(
                "Error while parsing JSON. Please check if all fields are correct from the docs",
            ),
            status: 400,
            details: None,
        },
        ErrorCodes::RawV1UserParsingError => APIError {
            code: 12,
            message: String::from(
                "Error while parsing JSON. Please check if all fields are correct from the docs",
            ),
            status: 400,
            details: Some(serde_json::to_value(["type", "value", "region", "queries"]).unwrap()),
        },
        ErrorCodes::RedisError => APIError {
            code: 13,
            message: String::from("Error while connectiong to Redis"),
            status: 500,
            details: None,
        },
        ErrorCodes::AssistV1NamesParsingError => APIError {
            code: 14,
            message: String::from(
                "Error while parsing JSON. Please check if all fields are correct from the docs",
            ),
            status: 400,
            details: Some(serde_json::to_value(["puuids"]).unwrap()),
        },
        ErrorCodes::PremierEndpointTemporaryIssues => APIError {
            code: 15,
            message: String::from("Some Premier Endpoint are currently facing issues. If you want to get notified on updates join https://discord.gg/X3GaVkX2YN. If you want to get atleast some data, you can use the /valorant/v1/premier/search endpoint. All endpoints listed in the 'details' property are facing issues right now."),
            status: 500,
            details: Some(serde_json::to_value(["/valorant/v1/premier/:name/:tag", "/valorant/v1/premier/:id"]).unwrap()),
        },
        ErrorCodes::PremierTeamNotFound => APIError {
            code: 16,
            message: String::from("Premier Team not found"),
            status: 404,
            details: None,
        },
        ErrorCodes::PremierSearchInvalidDivision => APIError {
            code: 17,
            message: String::from("Query 'division' must be a valid number"),
            status: 400,
            details: None,
        },
        ErrorCodes::PremierSearchInvalidDivisionSize => APIError {
            code: 18,
            message: String::from("Query 'division' must be a valid number between 1 and 21"),
            status: 400,
            details: None,
        },
        ErrorCodes::PremierSearchInvalidConference => APIError {
            code: 19,
            message: String::from("Invalid premier conference"),
            status: 400,
            details: None,
        },
        ErrorCodes::PremierSearchMultipleQueries => APIError {
            code: 20,
            message: String::from("Multiple queries detected. Make sure to only search by name/tag OR by ID"),
            status: 400,
            details: None,
        },
        ErrorCodes::DatabaseError => APIError {
            code: 21,
            message: String::from("Error while connecting to the database"),
            status: 500,
            details: None,
        },
        ErrorCodes::AccountNotFound => APIError {
            code: 22,
            message: String::from("Account not found"),
            status: 404,
            details: None,
        },
        ErrorCodes::RegionNotFound => APIError {
            code: 23,
            message: String::from("Region not found. Please ask the user to play a game (can be deathmatch or whatever)"),
            status: 404,
            details: None,
        },
        ErrorCodes::AccountMatchFetchError => APIError {
            code: 24,
            message: String::from("Error while fetching needed match data to get players account level and more data. Please ask the user to play a game (can be deathmatch or whatever)"),
            status: 404,
            details: None,
        },
        ErrorCodes::MMRNoDataAvailable => APIError {
            code: 25,
            message: String::from("No MMR data available for this user"),
            status: 404,
            details: None,
        },
        ErrorCodes::MatchNotFound => APIError {
            code: 26,
            message: String::from("Match not found"),
            status: 404,
            details: None,
        },
        ErrorCodes::InvalidMode => APIError {
            code: 27,
            message: String::from("Invalid mode"),
            status: 400,
            details: None,
            /*details: {
                let cache = OFFICER_API_CONTENT_GAMEMODES_QUEUES.read().await;
                let cache = cache.iter().filter(|i| !i.queueId.contains("console")).map(|i| i.selectedText.to_lowercase().replace(" ", "")).collect::<Vec<String>>();
                serde_json::to_value(cache).ok()
            },*/
        },
        ErrorCodes::InvalidMap => APIError {
            code: 28,
            message: String::from("Invalid map"),
            status: 400,
            details: None,
            /*details: {
                let cache = OFFICER_API_MAPS.lock().unwrap();
                let cache = cache.iter().map(|i| i.displayName.to_lowercase()).collect::<Vec<String>>();
                serde_json::to_value(cache).ok()
            },*/
        },
        ErrorCodes::MissingSizeQuery => APIError {
            code: 29,
            message: String::from("Missing \"size\" query"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidSizeOrPageQuery => APIError {
            code: 30,
            message: String::from("Query \"size\" and \"page\" must be a number"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidSizeOfSize => APIError {
            code: 31,
            message: String::from("Query \"size\" must be a number greater than 0"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidSizeOfPage => APIError {
            code: 32,
            message: String::from("Query \"page\" must be a number greater than 0"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidSeason => APIError {
            code: 33,
            message: String::from("Invalid season"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidNameQuery => APIError {
            code: 34,
            message: String::from("Query \"name\" is required"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidTagQuery => APIError {
            code: 35,
            message: String::from("Query \"tag\" is not allowed"),
            status: 400,
            details: None,
        },
        ErrorCodes::NotFoundInLeaderboard => APIError {
            code: 36,
            message: String::from("Player not found in leaderboard"),
            status: 404,
            details: None,
        },
        ErrorCodes::InvalidLeague => APIError {
            code: 37,
            message: String::from("Invalid league"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidLocaleContent => APIError {
            code: 38,
            message: String::from("Invalid locale"),
            status: 400,
            details: Some(serde_json::to_value(VALORANT_CONTENT_LOCALS).unwrap()),
        },
        ErrorCodes::InvalidCrosshairCode => APIError {
            code: 39,
            message: String::from("Invalid crosshair code"),
            status: 400,
            details: None,
        },
        ErrorCodes::DuplicateSeasonQuery => APIError {
            code: 40,
            message: String::from("Please only supply one season query (\":season_short\" or \":season_id\") at a time"),
            status: 400,
            details: None,
        },
        ErrorCodes::LeaderboardMetadataNotFound => APIError {
            code: 41,
            message: String::from("Leaderboard metadata not found"),
            status: 404,
            details: None,
        },
        ErrorCodes::InvalidPlatform => APIError {
            code: 42,
            message: String::from("Invalid platform"),
            status: 400,
            details: Some(serde_json::to_value(VALORANT_PLATFORMS).unwrap()),
        },
        ErrorCodes::InvalidUUID => APIError {
            code: 43,
            message: String::from("Invalid UUID/PUUID"),
            status: 400,
            details: None,
        },
        ErrorCodes::PremierSearchInvalidDivisionSizeSuper => APIError {
            code: 44,
            message: String::from("Query 'division' must be 22 for super divisions"),
            status: 400,
            details: None,
        },
        ErrorCodes::InvalidStartIndex => APIError {
            code: 45,
            message: String::from("Query 'start' must be a valid number greater than 0"),
            status: 400,
            details: None,
        },
        ErrorCodes::RiotImplementationRemoved => APIError {
            code: 46,
            message: String::from("Riot has removed this implementation. Please check the changelog for more information"),
            status: 404,
            details: None,
        },
        _ => APIError {
            code: 1,
            message: String::from("Internal Error"),
            status: 500,
            details: None,
        },
    }
}

pub fn get_base_error(error: u16) -> ErrorCodes {
    match error {
        400 => ErrorCodes::Base400,
        401 => ErrorCodes::Base401,
        403 => ErrorCodes::Base403,
        404 => ErrorCodes::Base404,
        _ => ErrorCodes::InternalError,
    }
}

pub fn error_handler(errors: Vec<ErrorCodes>) -> Response {
    let mut obj = SendError { errors: vec![] };
    for x in errors.iter() {
        obj.errors.push(get_error(x))
    }
    let status_code: StatusCode = StatusCode::from_u16(match obj.errors.first() {
        None => 500,
        Some(v) => v.status,
    })
    .unwrap();
    // json responmse
    let body = serde_json::to_string(&obj).unwrap();
    Response::builder()
        .status(status_code)
        .header(CONTENT_TYPE, "application/json")
        .header("Access-Control-Allow-Origin", "*")
        .body(Body::from(body))
        .unwrap()
}

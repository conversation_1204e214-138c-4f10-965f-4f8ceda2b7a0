use utoipa::OpenApi;

#[derive(OpenApi)]
#[openapi(
    info(title = "HenrikDev API", version = "4.3.0"),
    servers((url = "/")),
    tags(
        (name = "valorant", description = "Valorant public API endpoints")
    ),
    paths(
        // account
        crate::routes::valorant::account::get_account_v1,
        crate::routes::valorant::account::get_account_by_id_v1,
        crate::routes::valorant::account::get_account_v2,
        crate::routes::valorant::account::get_account_by_id_v2,
        // mmr
        crate::routes::valorant::mmr::get_mmr_v1_by_id,
        crate::routes::valorant::mmr::get_mmr_v1_by_name,
        crate::routes::valorant::mmr::get_mmr_v2_by_id,
        crate::routes::valorant::mmr::get_mmr_v2_by_name,
        crate::routes::valorant::mmr::get_mmr_v3_by_name,
        crate::routes::valorant::mmr::get_mmr_v3_by_id,
        // mmr history
        crate::routes::valorant::mmr_history::get_mmr_history_by_name,
        crate::routes::valorant::mmr_history::get_mmr_history_by_id,
        crate::routes::valorant::mmr_history::get_mmr_history_v2_by_name,
        crate::routes::valorant::mmr_history::get_mmr_history_v2_by_id,
        // matches
        crate::routes::valorant::matches::get_matches_v3_by_name,
        crate::routes::valorant::matches::get_matches_v3_by_id,
        crate::routes::valorant::matches::get_matches_v4_by_name,
        crate::routes::valorant::matches::get_matches_v4_by_id,
        // match details
        crate::routes::valorant::r#match::match_v2,
        crate::routes::valorant::r#match::match_v4,
        // leaderboard
        crate::routes::valorant::leaderboard::leaderboard_v1,
        crate::routes::valorant::leaderboard::leaderboard_v2,
        crate::routes::valorant::leaderboard::leaderboard_v3,
        // premier
        crate::routes::valorant::premier::premier_by_name,
        crate::routes::valorant::premier::premier_by_id,
        crate::routes::valorant::premier::premier_by_name_history,
        crate::routes::valorant::premier::premier_by_id_history,
        crate::routes::valorant::premier::premier_search,
        crate::routes::valorant::premier::premier_leaderboard,
        // stored endpoints
        crate::routes::valorant::stored_endpoints::stored_matches,
        crate::routes::valorant::stored_endpoints::stored_matches_by_id,
        crate::routes::valorant::stored_endpoints::stored_mmr_history,
        crate::routes::valorant::stored_endpoints::stored_mmr_history_by_id,
        crate::routes::valorant::stored_endpoints::stored_mmr_history_v2,
        crate::routes::valorant::stored_endpoints::stored_mmr_history_v2_by_id,
        // status and queue
        crate::routes::valorant::status::Status,
        crate::routes::valorant::queue_status::QueueStatus,
        // content and esports
        crate::routes::valorant::content::get_content_v1,
        crate::routes::valorant::esports::esports_schedules_v1,
        // store
        crate::routes::valorant::store_featured::StoreFeatured,
        crate::routes::valorant::store_offers::StoreOffers,
        // misc
        crate::routes::valorant::version::Version,
        crate::routes::valorant::website::Website,
        crate::routes::valorant::raw::Raw,
        crate::routes::valorant::crosshair::crosshair,
    )
)]
pub struct ApiDoc;
